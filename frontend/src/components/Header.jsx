import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaBars, FaTimes, FaUserCircle, FaSearch, FaHome, FaInfoCircle, FaSignOutAlt, FaUpload, FaUser } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import LanguageSelector from './LanguageSelector';

/**
 * Header component with responsive design for both desktop and mobile views
 */
const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user, logout } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  const handleLogout = () => {
    if (window.confirm(t('header.logoutConfirm'))) {
      logout();
      navigate('/');
      setIsMobileMenuOpen(false);
    }
  };

  // Close mobile menu when clicking outside or on escape
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        setIsMobileMenuOpen(false);
      }
    };

    const handleClickOutside = (e) => {
      if (isMobileMenuOpen && !e.target.closest('.mobile-menu') && !e.target.closest('.mobile-menu-button')) {
        setIsMobileMenuOpen(false);
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener('keydown', handleEscape);
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isMobileMenuOpen]);

  return (
    <header className="w-full bg-[#151515] sticky top-0 z-[1000] shadow-[0_2px_10px_rgba(0,0,0,0.3)]">
      {/* Main header container */}
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Top row - Logo, Desktop Nav, Mobile Menu Button */}
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link
              to="/"
              className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-[#f44336] to-[#ff9800] bg-clip-text text-transparent hover:scale-105 transition-transform duration-300"
            >
              {t('header.brand')}
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Search Button */}
            <Link
              to="/search"
              className="flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-[#f44336] to-[#ff9800] text-white font-semibold shadow-lg hover:shadow-xl hover:from-[#d32f2f] hover:to-[#f57c00] transition-all duration-300"
            >
              <FaSearch className="mr-2 text-sm" />
              <span>{t('header.search')}</span>
            </Link>

            {/* Upload Game Button */}
            <Link
              to="/upload-game"
              className="flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-[#7b52ff] to-[#9b7dff] text-white font-semibold shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all duration-300"
            >
              <FaUpload className="mr-2 text-sm" />
              <span>{t('header.uploadGame')}</span>
            </Link>

            {/* Language Selector */}
            <LanguageSelector />
          </div>

          {/* Desktop User Actions */}
          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              <>
                {/* User Profile */}
                <Link 
                  to="/profile" 
                  className="flex items-center text-white hover:text-gray-300 transition-colors duration-300"
                >
                  <FaUser className="mr-2" />
                  <span>{t('header.profile')}</span>
                </Link>

                {/* Logout Button */}
                <button
                  onClick={handleLogout}
                  className="px-4 py-2 bg-[#f44336] text-white rounded-full hover:bg-[#d32f2f] transition-colors duration-300"
                >
                  {t('header.logout')}
                </button>
              </>
            ) : (
              /* Login Button */
              <Link 
                to="/login" 
                className="flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-[#4a65ff] to-[#5e3bff] text-white font-semibold shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all duration-300"
              >
                <FaUserCircle className="mr-2" />
                <span>{t('header.login')}</span>
              </Link>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="mobile-menu-button p-2 text-white hover:text-gray-300 transition-colors duration-300"
              aria-label="Toggle menu"
            >
              <FaBars className="text-xl" />
            </button>
          </div>
        </div>

        {/* Mobile Search Button - Show below header on mobile */}
        <div className="md:hidden pb-4">
          <Link 
            to="/search" 
            className="flex items-center justify-center w-full px-4 py-3 rounded-full bg-gradient-to-r from-[#f44336] to-[#ff9800] text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <FaSearch className="mr-2 text-sm" />
            <span>{t('header.search')}</span>
          </Link>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden" onClick={() => setIsMobileMenuOpen(false)} />
      )}

      {/* Mobile Menu */}
      <div className={`mobile-menu fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-[#151515] transform transition-transform duration-300 ease-in-out z-50 md:hidden ${
        isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        {/* Mobile Menu Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-white">Menu</h2>
          <button 
            onClick={() => setIsMobileMenuOpen(false)}
            className="p-2 text-white hover:text-gray-300 transition-colors duration-300"
            aria-label="Close menu"
          >
            <FaTimes className="text-xl" />
          </button>
        </div>

        {/* Mobile Menu Content */}
        <nav className="p-4 space-y-2">
          {/* Home */}
          <Link 
            to="/" 
            className="flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-300"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            <FaHome className="text-lg" />
            <span>{t('header.home')}</span>
          </Link>

          {/* Upload Game */}
          <Link
            to="/upload-game"
            className="flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-300"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            <FaUpload className="text-lg" />
            <span>{t('header.uploadGame')}</span>
          </Link>

          {/* User-specific menu items */}
          {user && (
            <>
              {/* Profile */}
              <Link 
                to="/profile"
                className="flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-300"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <FaUser className="text-lg" />
                <span>{t('header.profile')}</span>
              </Link>

              {/* Logout */}
              <button
                onClick={handleLogout}
                className="w-full flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-300 text-left"
              >
                <FaSignOutAlt className="text-lg" />
                <span>{t('header.logout')}</span>
              </button>
            </>
          )}

          {/* About */}
          <Link
            to="/about"
            className="flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-300"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            <FaInfoCircle className="text-lg" />
            <span>{t('header.about')}</span>
          </Link>

          {/* Language Selector for Mobile */}
          <div className="px-4 py-3">
            <LanguageSelector />
          </div>

          {/* Authentication for non-logged in users */}
          {!user && (
            <Link
              to="/login"
              className="flex items-center space-x-3 px-4 py-3 bg-gradient-to-r from-[#4a65ff] to-[#5e3bff] text-white hover:from-[#3a55ff] hover:to-[#4e2bff] rounded-lg transition-all duration-300"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <FaUserCircle className="text-lg" />
              <span>{t('header.login')}</span>
            </Link>
          )}
        </nav>
      </div>
    </header>
  );
};

export default Header;
