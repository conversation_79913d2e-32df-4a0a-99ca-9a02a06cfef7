import { Link, useLocation } from 'react-router-dom';
import {
  FaHome,
  FaClock,
  FaStar,
  FaFire,
  FaSync,
  FaCrown,
  FaUsers,
  FaUserFriends,
  FaFistRaised,
  FaMap,
  FaBasketballBall,
  FaHeart,
  FaBicycle,
  FaCar,
  FaClone,
  FaGamepad,
  FaMouse,
  FaTshirt,
  FaRoad,
  FaDoorOpen,
  FaBolt,
  FaCrosshairs,
  FaGhost,
  FaBars,
  FaTimes
} from 'react-icons/fa';
import { useLanguage } from '../context/LanguageContext';
import { useSidebar } from '../context/SidebarContext';
import PropTypes from 'prop-types';

const Sidebar = ({ isOpen, onToggle }) => {
  const { t } = useLanguage();
  const location = useLocation();
  const { isInitialized } = useSidebar();

  // Navigation items with their corresponding icons and routes
  const navigationItems = [
    { key: 'home', icon: FaHome, route: '/', color: 'text-blue-400' },
    { key: 'recentlyPlayed', icon: FaClock, route: '/recently-played', color: 'text-green-400' },
    { key: 'new', icon: FaStar, route: '/new', color: 'text-yellow-400' },
    { key: 'trendingNow', icon: FaFire, route: '/trending', color: 'text-red-400' },
    { key: 'updated', icon: FaSync, route: '/updated', color: 'text-cyan-400' },
    { key: 'originals', icon: FaCrown, route: '/originals', color: 'text-purple-400' },
    { key: 'multiplayer', icon: FaUsers, route: '/multiplayer', color: 'text-pink-400' },
  ];

  const categoryItems = [
    { key: 'twoPlayer', icon: FaUserFriends, route: '/category/2-player', color: 'text-indigo-400' },
    { key: 'action', icon: FaFistRaised, route: '/category/action', color: 'text-red-500' },
    { key: 'adventure', icon: FaMap, route: '/category/adventure', color: 'text-green-500' },
    { key: 'basketball', icon: FaBasketballBall, route: '/category/basketball', color: 'text-orange-500' },
    { key: 'beauty', icon: FaHeart, route: '/category/beauty', color: 'text-pink-500' },
    { key: 'bike', icon: FaBicycle, route: '/category/bike', color: 'text-blue-500' },
    { key: 'car', icon: FaCar, route: '/category/car', color: 'text-gray-400' },
    { key: 'card', icon: FaClone, route: '/category/card', color: 'text-red-600' },
    { key: 'casual', icon: FaGamepad, route: '/category/casual', color: 'text-teal-400' },
    { key: 'clicker', icon: FaMouse, route: '/category/clicker', color: 'text-yellow-500' },
    { key: 'controller', icon: FaGamepad, route: '/category/controller', color: 'text-purple-500' },
    { key: 'dressUp', icon: FaTshirt, route: '/category/dress-up', color: 'text-pink-600' },
    { key: 'driving', icon: FaRoad, route: '/category/driving', color: 'text-gray-500' },
    { key: 'escape', icon: FaDoorOpen, route: '/category/escape', color: 'text-orange-400' },
    { key: 'flash', icon: FaBolt, route: '/category/flash', color: 'text-yellow-600' },
    { key: 'fps', icon: FaCrosshairs, route: '/category/fps', color: 'text-red-700' },
    { key: 'horror', icon: FaGhost, route: '/category/horror', color: 'text-gray-600' },
  ];

  const isActiveRoute = (route) => {
    return location.pathname === route;
  };

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed top-0 left-0 h-full w-64 bg-[#1a1a1a] border-r border-gray-800 z-50 transform ${isInitialized ? 'transition-transform duration-300 ease-in-out' : ''} overflow-y-auto
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:${isOpen ? 'translate-x-0' : '-translate-x-full'}
        [&::-webkit-scrollbar]:w-0 [&::-webkit-scrollbar]:hover:w-2
        hover:[&::-webkit-scrollbar]:w-2
        [&::-webkit-scrollbar-track]:bg-transparent
        [&::-webkit-scrollbar-thumb]:bg-gray-600
        [&::-webkit-scrollbar-thumb]:rounded-full
        [&::-webkit-scrollbar-thumb]:hover:bg-gray-500
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          <h2 className="text-white font-semibold text-lg">Navigation</h2>
          <button
            onClick={onToggle}
            className="lg:hidden text-gray-400 hover:text-white transition-colors"
            aria-label={t('sidebar.toggle.close')}
          >
            <FaTimes size={20} />
          </button>
        </div>

        {/* Navigation Items */}
        <nav className="p-4">
          <div className="space-y-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = isActiveRoute(item.route);
              
              return (
                <Link
                  key={item.key}
                  to={item.route}
                  className={`
                    flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 group
                    ${isActive 
                      ? 'bg-gray-800 text-white' 
                      : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    }
                  `}
                  onClick={() => {
                    // Close sidebar on mobile when item is clicked
                    if (window.innerWidth < 1024) {
                      onToggle();
                    }
                  }}
                >
                  <Icon className={`text-lg ${item.color} group-hover:scale-110 transition-transform`} />
                  <span className="font-medium">{t(`sidebar.navigation.${item.key}`)}</span>
                </Link>
              );
            })}
          </div>

          {/* Divider */}
          <div className="my-4 border-t border-gray-700"></div>

          {/* Category Items */}
          <div className="space-y-1">
            {categoryItems.map((item) => {
              const Icon = item.icon;
              const isActive = isActiveRoute(item.route);
              
              return (
                <Link
                  key={item.key}
                  to={item.route}
                  className={`
                    flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 group
                    ${isActive 
                      ? 'bg-gray-800 text-white' 
                      : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    }
                  `}
                  onClick={() => {
                    // Close sidebar on mobile when item is clicked
                    if (window.innerWidth < 1024) {
                      onToggle();
                    }
                  }}
                >
                  <Icon className={`text-lg ${item.color} group-hover:scale-110 transition-transform`} />
                  <span className="font-medium">{t(`sidebar.navigation.${item.key}`)}</span>
                </Link>
              );
            })}
          </div>
        </nav>
      </div>

      {/* Toggle Button - Fixed position */}
      <button
        onClick={onToggle}
        className={`
          fixed top-20 z-50 bg-[#1a1a1a] border border-gray-700 text-white p-3 rounded-r-lg shadow-lg ${isInitialized ? 'transition-all duration-300' : ''} hover:bg-gray-800
          ${isOpen ? 'left-64' : 'left-0'}
        `}
        aria-label={isOpen ? t('sidebar.toggle.close') : t('sidebar.toggle.open')}
      >
        {isOpen ? <FaTimes size={20} /> : <FaBars size={20} />}
      </button>
    </>
  );
};

Sidebar.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onToggle: PropTypes.func.isRequired,
};

export default Sidebar;
